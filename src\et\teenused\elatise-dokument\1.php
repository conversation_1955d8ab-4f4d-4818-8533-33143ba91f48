<!DOCTYPE html>
<html lang="en" class="relative min-h-full">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <link rel="canonical" href="https://preline.co/">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Coffee Shop demo showcases a modern, elegant interface for browsing seasonal products, exploring artisanal beverages, and experiencing a unique blend of coffee culture and artistic ambiance.">

    <meta name="twitter:site" content="@preline">
    <meta name="twitter:creator" content="@preline">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Coffee Shop | Preline Pro | Preline UI, crafted with Tailwind CSS">
    <meta name="twitter:description" content="Coffee Shop demo showcases a modern, elegant interface for browsing seasonal products, exploring artisanal beverages, and experiencing a unique blend of coffee culture and artistic ambiance.">
    <meta name="twitter:image" content="https://preline.co/assets/img/og-image.png">

    <meta property="og:url" content="https://preline.co/">
    <meta property="og:locale" content="en_US">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Preline">
    <meta property="og:title" content="Coffee Shop | Preline Pro | Preline UI, crafted with Tailwind CSS">
    <meta property="og:description" content="Coffee Shop demo showcases a modern, elegant interface for browsing seasonal products, exploring artisanal beverages, and experiencing a unique blend of coffee culture and artistic ambiance.">
    <meta property="og:image" content="https://preline.co/assets/img/og-image.png">

    <!-- Title -->
    <title>Coffee Shop | Preline Pro | Preline UI, crafted with Tailwind CSS</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="../favicon.ico">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS HS -->

    <link rel="stylesheet" href="../../../assets/css/main.min.css?v=3.1.0">

    <!-- Theme Check and Update -->
    <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
    </script>
</head>

<body class="dark:bg-neutral-900">
    <!-- ========== HEADER ========== -->
    <header class="">

        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

        <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
    </header>
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content">
        <div class="max-w-2xl px-4 sm:px-6 lg:px-8 py-12 lg:py-24 mx-auto">
            <div class="max-w-2xl text-center mx-auto">
                <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white mb-4">
                    Elatise dokument
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                    Juristi poolt elatise dokumendi koostamine
                </p>
            </div>

            <div class="lg:max-w-xl mx-auto">
                <div class="bg-white dark:bg-neutral-800 p-4">

                    <div class="prose prose-gray max-w-none dark:prose-invert">
                        <div class="pb-4 mb-8 border-b border-gray-100 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
                            <p class="text-gray-600 dark:text-gray-300 mb-4">
                                Koostame Sinu vajadustele vastava juriidiliselt korrektse <span class="font-semibold text-orange-500">elatise dokumendi</span>, mida saad kasutada teise vanema poole pöördumiseks või kohtule esitamiseks.
                            </p>

                            <p class="text-gray-600 dark:text-gray-300 mb-6">
                                Dokumendi saamiseks sisesta andmed ja vasta küsimustele. Seejärel saad tasuda teenustasu summas <span class="font-semibold text-orange-500"><?php echo "69"; ?>€</span> pangalingi kaudu.
                            </p>
                            <p class="text-gray-600 dark:text-gray-300 mb-6">
                                Makse laekumise järel alustame dokumendi koostamist. Valmis dokumendi saadame Sulle e-kirja teel hiljemalt <span class="font-semibold text-orange-500"><?php echo date('d.m.Y', strtotime('+5 days')); ?></span>. Vajadusel võtame Sinuga ühendust, et täpsustada asjaolusid.
                            </p>
                        </div>


                        <!-- Order Summary -->
                        <div class="bg-gray-50 dark:bg-neutral-700 rounded-xl p-6 mb-8">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-neutral-200 mb-4">Tellimuse ülevaade</h3>

                            <div class="space-y-4">
                                <div class="flex justify-between text-gray-800 dark:text-neutral-200">
                                    <span class="font-medium">Teenuse nimetus</span>
                                    <span>Elatise dokument</span>
                                </div>

                                <div class="flex justify-between text-gray-800 dark:text-neutral-200">
                                    <span class="font-medium">Dokumendi valmimine</span>
                                    <span>20.06.2025</span>
                                </div>

                                <div class="flex justify-between text-lg font-semibold text-gray-900 dark:text-neutral-100 pt-4 border-t border-gray-200 dark:border-neutral-600">
                                    <span>Hind</span>
                                    <span class="text-orange-500">69.00€</span>
                                </div>
                            </div>
                        </div>


                        <!-- Button Group -->
                        <div class="flex flex-col gap-y-3 pt-6">
                            <button onclick="window.location.href='2.php'"
                                class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl bg-orange-500 text-lg text-white hover:bg-[#F86A1A] focus:outline-none transition">
                                Alusta siit
                            </button>

                            <a href="../../../et/teenused/elatise-dokument.php"
                                class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-2 font-medium rounded-xl text-orange-500 hover:bg-gray-100 dark:text-orange-500 dark:hover:bg-neutral-800 transition">
                                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path d="M15 18l-6-6 6-6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                Tagasi
                            </a>
                        </div>
                        <!-- End Button Group -->
                    </div>

                </div>
            </div>

        </div>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== FOOTER ========== -->

    <!-- ========== END FOOTER ========== -->

    <!-- JS PLUGINS -->
    <!-- Required plugins -->
    <script src="../../../assets/vendor/preline/dist/index.js?v=3.1.0"></script>
    <!-- Clipboard -->
    <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
    <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

    <script>
        window.addEventListener('load', () => {
            confetti({
                particleCount: 100,
                spread: 70,
                origin: {
                    y: 0.6
                }
            });
        });
    </script>

</body>

</html>